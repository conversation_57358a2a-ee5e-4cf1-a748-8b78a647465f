# apps/accounting/templatetags/accounting_tags.py
from django import template
from django.utils.safestring import mark_safe
from ..services.helpers import format_currency, get_classe_libelle

register = template.Library()

@register.filter
def format_currency_tag(amount, currency='FCFA'):
    """Formater un montant en devise"""
    return format_currency(amount, currency)

@register.filter
def classe_libelle(classe):
    """Retourner le libellé d'une classe comptable"""
    return get_classe_libelle(classe)

@register.filter
def account_nature_badge(nature):
    """Afficher un badge coloré selon la nature du compte"""
    badges = {
        'ACTIF': 'badge bg-primary',
        'PASSIF': 'badge bg-secondary', 
        'CHARGE': 'badge bg-danger',
        'PRODUIT': 'badge bg-success'
    }
    
    badge_class = badges.get(nature, 'badge bg-light')
    return mark_safe(f'<span class="{badge_class}">{nature}</span>')

@register.filter
def ecriture_status_badge(statut):
    """Afficher un badge coloré selon le statut de l'écriture"""
    badges = {
        'BROUILLARD': 'badge bg-warning',
        'VALIDEE': 'badge bg-success',
        'CLOTUREE': 'badge bg-secondary'
    }
    
    badge_class = badges.get(statut, 'badge bg-light')
    return mark_safe(f'<span class="{badge_class}">{statut}</span>')

@register.filter
def journal_type_icon(type_journal):
    """Afficher une icône selon le type de journal"""
    icons = {
        'VENTE': 'bi-cart-plus',
        'ACHAT': 'bi-cart-dash',
        'BANQUE': 'bi-bank',
        'CAISSE': 'bi-cash-coin',
        'OD': 'bi-journal-text'
    }
    
    icon_class = icons.get(type_journal, 'bi-journal')
    return mark_safe(f'<i class="bi {icon_class}"></i>')

@register.filter
def alert_level_class(niveau):
    """Retourner la classe CSS selon le niveau d'alerte"""
    classes = {
        'CRITICAL': 'alert-danger',
        'WARNING': 'alert-warning',
        'INFO': 'alert-info'
    }
    return classes.get(niveau, 'alert-secondary')

@register.filter
def alert_level_icon(niveau):
    """Retourner l'icône selon le niveau d'alerte"""
    icons = {
        'CRITICAL': 'bi-exclamation-triangle-fill text-danger',
        'WARNING': 'bi-exclamation-triangle text-warning',
        'INFO': 'bi-info-circle text-info'
    }
    
    icon_class = icons.get(niveau, 'bi-info-circle')
    return mark_safe(f'<i class="bi {icon_class}"></i>')

@register.simple_tag
def accounting_progress_bar(current, total, label=""):
    """Générer une barre de progression"""
    if total == 0:
        percentage = 0
    else:
        percentage = min((current / total) * 100, 100)
    
    return mark_safe(f'''
        <div class="progress" style="height: 20px;">
            <div class="progress-bar" role="progressbar" 
                 style="width: {percentage}%" 
                 aria-valuenow="{current}" 
                 aria-valuemin="0" 
                 aria-valuemax="{total}">
                {label} {percentage:.1f}%
            </div>
        </div>
    ''')

@register.inclusion_tag('accounting/components/kpi_card.html')
def kpi_card(title, value, icon, color="primary", subtitle=""):
    """Composant réutilisable pour les cartes KPI"""
    return {
        'title': title,
        'value': value,
        'icon': icon,
        'color': color,
        'subtitle': subtitle
    }

@register.inclusion_tag('accounting/components/compte_tree_item.html')
def compte_tree_item(compte, level=0):
    """Composant récursif pour l'arbre des comptes"""
    return {
        'compte': compte,
        'level': level,
        'has_children': compte.get('children', [])
    }

@register.filter
def multiply(value, arg):
    """Multiplier deux valeurs"""
    try:
        return float(value) * float(arg)
    except (ValueError, TypeError):
        return 0

@register.filter
def percentage(value, total):
    """Calculer un pourcentage"""
    try:
        if float(total) == 0:
            return 0
        return (float(value) / float(total)) * 100
    except (ValueError, TypeError):
        return 0

@register.filter
def abs_value(value):
    """Valeur absolue"""
    try:
        return abs(float(value))
    except (ValueError, TypeError):
        return 0
