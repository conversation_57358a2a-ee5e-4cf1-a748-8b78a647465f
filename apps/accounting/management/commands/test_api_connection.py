# apps/accounting/management/commands/test_api_connection.py
from django.core.management.base import BaseCommand
from django.conf import settings
from apps.accounting.services.api_client import api_client
import json

class Command(BaseCommand):
    help = 'Test la connexion à l\'API comptabilité'

    def handle(self, *args, **options):
        self.stdout.write(self.style.HTTP_INFO('=== Test de Connexion API Comptabilité ==='))
        
        # Vérifier la configuration
        self.stdout.write('\n1. Configuration:')
        self.stdout.write(f'   API URL: {settings.COMPTABILITE_API_URL}')
        self.stdout.write(f'   API Key: {settings.COMPTABILITE_API_KEY[:10]}...' if settings.COMPTABILITE_API_KEY else '   API Key: NON CONFIGURÉE')
        self.stdout.write(f'   Société ID: {settings.COMPTABILITE_SOCIETE_ID}')
        self.stdout.write(f'   Société Nom: {settings.COMPTABILITE_SOCIETE_NOM}')
        
        if not settings.COMPTABILITE_API_KEY:
            self.stdout.write(self.style.ERROR('\n❌ ERREUR: COMPTABILITE_API_KEY non configurée'))
            return
        
        # Test de connexion
        self.stdout.write('\n2. Test de connexion...')
        try:
            response = api_client.get_config()
            
            if response.success:
                self.stdout.write(self.style.SUCCESS('✅ Connexion réussie !'))
                
                if response.data:
                    self.stdout.write('\n3. Données reçues:')
                    self.stdout.write(json.dumps(response.data, indent=2, ensure_ascii=False))
                
            else:
                self.stdout.write(self.style.ERROR('❌ Échec de connexion'))
                self.stdout.write(f'   Erreur: {response.error}')
                self.stdout.write(f'   Status: {response.status_code}')
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Exception: {str(e)}'))
        
        # Test des autres endpoints
        self.stdout.write('\n4. Test des endpoints principaux...')
        
        endpoints_to_test = [
            ('KPIs', lambda: api_client.get_kpi_financiers('2024-01-01', '2024-12-31')),
            ('Comptes', lambda: api_client.get_comptes()),
            ('Journaux', lambda: api_client.get_journaux()),
            ('Alertes', lambda: api_client.get_alertes()),
        ]
        
        for name, test_func in endpoints_to_test:
            try:
                response = test_func()
                if response.success:
                    self.stdout.write(f'   ✅ {name}: OK')
                else:
                    self.stdout.write(f'   ❌ {name}: {response.error.get("message", "Erreur") if response.error else "Erreur inconnue"}')
            except Exception as e:
                self.stdout.write(f'   ❌ {name}: Exception - {str(e)}')
        
        self.stdout.write(self.style.HTTP_INFO('\n=== Fin du test ==='))
