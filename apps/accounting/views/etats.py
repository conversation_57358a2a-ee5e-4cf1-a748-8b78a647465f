# apps/accounting/views/etats.py
from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.contrib import messages
import logging

from ..services.api_client import api_client
from ..services.helpers import handle_api_response, get_current_exercice_dates

logger = logging.getLogger(__name__)

@login_required
def etats_menu(request):
    """Menu des états comptables"""
    
    context = {
        'page_title': 'États Comptables',
        'section': 'accounting'
    }
    
    return render(request, 'accounting/etats_menu.html', context)

@login_required
def balance_generale(request):
    """Balance générale"""
    
    # Récupérer les paramètres
    date_debut = request.GET.get('dateDebut')
    date_fin = request.GET.get('dateFin')
    niveau = int(request.GET.get('niveau', 3))
    
    # Utiliser les dates de l'exercice par défaut
    if not date_debut or not date_fin:
        exercice_dates = get_current_exercice_dates()
        date_debut = date_debut or exercice_dates['debut']
        date_fin = date_fin or exercice_dates['fin']
    
    balance = None
    if request.GET.get('generate'):
        response = api_client.get_balance(date_debut, date_fin, niveau)
        balance = handle_api_response(response, request)
    
    context = {
        'balance': balance,
        'date_debut': date_debut,
        'date_fin': date_fin,
        'niveau': niveau,
        'page_title': 'Balance Générale',
        'section': 'accounting'
    }
    
    return render(request, 'accounting/balance.html', context)

@login_required
def bilan(request):
    """Bilan comptable"""
    
    date_arrete = request.GET.get('dateArrete')
    
    # Utiliser la fin d'exercice par défaut
    if not date_arrete:
        exercice_dates = get_current_exercice_dates()
        date_arrete = exercice_dates['fin']
    
    bilan_data = None
    if request.GET.get('generate'):
        response = api_client.get_bilan(date_arrete)
        bilan_data = handle_api_response(response, request)
    
    context = {
        'bilan': bilan_data,
        'date_arrete': date_arrete,
        'page_title': 'Bilan Comptable',
        'section': 'accounting'
    }
    
    return render(request, 'accounting/bilan.html', context)

@login_required
def compte_resultat(request):
    """Compte de résultat"""
    
    date_debut = request.GET.get('dateDebut')
    date_fin = request.GET.get('dateFin')
    
    # Utiliser les dates de l'exercice par défaut
    if not date_debut or not date_fin:
        exercice_dates = get_current_exercice_dates()
        date_debut = date_debut or exercice_dates['debut']
        date_fin = date_fin or exercice_dates['fin']
    
    compte_resultat_data = None
    if request.GET.get('generate'):
        response = api_client.get_compte_resultat(date_debut, date_fin)
        compte_resultat_data = handle_api_response(response, request)
    
    context = {
        'compte_resultat': compte_resultat_data,
        'date_debut': date_debut,
        'date_fin': date_fin,
        'page_title': 'Compte de Résultat',
        'section': 'accounting'
    }
    
    return render(request, 'accounting/compte_resultat.html', context)

@login_required
def grand_livre(request):
    """Grand livre par compte"""
    
    compte_numero = request.GET.get('compteNumero')
    date_debut = request.GET.get('dateDebut')
    date_fin = request.GET.get('dateFin')
    
    # Utiliser les dates de l'exercice par défaut
    if not date_debut or not date_fin:
        exercice_dates = get_current_exercice_dates()
        date_debut = date_debut or exercice_dates['debut']
        date_fin = date_fin or exercice_dates['fin']
    
    grand_livre_data = None
    if compte_numero and request.GET.get('generate'):
        response = api_client.get_grand_livre(compte_numero, date_debut, date_fin)
        grand_livre_data = handle_api_response(response, request)
    
    context = {
        'grand_livre': grand_livre_data,
        'compte_numero': compte_numero,
        'date_debut': date_debut,
        'date_fin': date_fin,
        'page_title': 'Grand Livre',
        'section': 'accounting'
    }
    
    return render(request, 'accounting/grand_livre.html', context)
