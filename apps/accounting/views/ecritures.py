# apps/accounting/views/ecritures.py
from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
import logging

from ..services.api_client import api_client
from ..services.helpers import handle_api_response

logger = logging.getLogger(__name__)

@login_required
def ecriture_list(request):
    """Liste des écritures comptables avec filtres"""
    
    # Récupérer les paramètres de filtre
    filters = {}
    if request.GET.get('dateDebut'):
        filters['dateDebut'] = request.GET.get('dateDebut')
    if request.GET.get('dateFin'):
        filters['dateFin'] = request.GET.get('dateFin')
    if request.GET.get('journalCode'):
        filters['journalCode'] = request.GET.get('journalCode')
    if request.GET.get('statut'):
        filters['statut'] = request.GET.get('statut')
    
    response = api_client.get_ecritures(filters)
    ecritures = handle_api_response(response, request) or []
    
    context = {
        'ecritures': ecritures,
        'page_title': 'Écritures Comptables',
        'section': 'accounting',
        'current_filters': filters
    }
    
    return render(request, 'accounting/ecriture_list.html', context)

@login_required
def ecriture_create(request):
    """Création d'une nouvelle écriture comptable"""
    
    if request.method == 'POST':
        # TODO: Implémenter la création d'écriture
        # Nécessite un formulaire complexe multi-lignes
        pass
    
    # Récupérer les journaux pour le formulaire
    journaux_response = api_client.get_journaux()
    journaux = handle_api_response(journaux_response, request) or []
    
    context = {
        'journaux': journaux,
        'page_title': 'Nouvelle Écriture',
        'section': 'accounting'
    }
    
    return render(request, 'accounting/ecriture_form.html', context)

@login_required
def ecriture_detail(request, pk):
    """Détail d'une écriture comptable"""
    
    response = api_client.get_ecriture(pk)
    ecriture = handle_api_response(response, request)
    
    if not ecriture:
        messages.error(request, "Écriture non trouvée")
        return redirect('accounting:ecriture_list')
    
    context = {
        'ecriture': ecriture,
        'page_title': f'Écriture {ecriture.get("numero", pk)}',
        'section': 'accounting'
    }
    
    return render(request, 'accounting/ecriture_detail.html', context)

@login_required
def ecriture_edit(request, pk):
    """Modification d'une écriture comptable"""
    
    if request.method == 'POST':
        # TODO: Implémenter la modification d'écriture
        pass
    
    response = api_client.get_ecriture(pk)
    ecriture = handle_api_response(response, request)
    
    if not ecriture:
        messages.error(request, "Écriture non trouvée")
        return redirect('accounting:ecriture_list')
    
    context = {
        'ecriture': ecriture,
        'page_title': f'Modifier Écriture {ecriture.get("numero", pk)}',
        'section': 'accounting'
    }
    
    return render(request, 'accounting/ecriture_form.html', context)
