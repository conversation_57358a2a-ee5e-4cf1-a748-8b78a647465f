# apps/accounting/views/plan_comptable.py
from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
import logging

from ..services.api_client import api_client
from ..services.helpers import handle_api_response

logger = logging.getLogger(__name__)

@login_required
def plan_comptable(request):
    """Affichage hiérarchique du plan comptable"""
    
    # Récupérer la hiérarchie des comptes
    response = api_client.get_hierarchie_comptes()
    
    if response.success:
        comptes = response.data
    else:
        messages.error(request, "Erreur lors du chargement du plan comptable")
        comptes = []
    
    context = {
        'comptes': comptes,
        'page_title': 'Plan Comptable SYSCOHADA',
        'section': 'accounting'
    }
    
    return render(request, 'accounting/plan_comptable.html', context)

@login_required
def compte_list(request):
    """Liste des comptes avec filtres"""
    
    # Récupérer les paramètres de filtre
    classe = request.GET.get('classe')
    actif = request.GET.get('actif')
    
    # Convertir les paramètres
    filters = {}
    if classe:
        filters['classe'] = int(classe)
    if actif is not None:
        filters['actif'] = actif.lower() == 'true'
    
    response = api_client.get_comptes(**filters)
    comptes = handle_api_response(response, request) or []
    
    context = {
        'comptes': comptes,
        'page_title': 'Liste des Comptes',
        'section': 'accounting',
        'current_filters': {
            'classe': classe,
            'actif': actif
        }
    }
    
    return render(request, 'accounting/compte_list.html', context)

@login_required
def compte_create(request):
    """Création d'un nouveau compte"""
    
    if request.method == 'POST':
        # Récupérer les données du formulaire
        compte_data = {
            'numero': request.POST.get('numero'),
            'libelle': request.POST.get('libelle'),
            'classe': int(request.POST.get('classe')),
            'nature': request.POST.get('nature'),
            'sens': request.POST.get('sens'),
            'niveau': int(request.POST.get('niveau', 3)),
            'compteParent': request.POST.get('compteParent') or None,
            'actif': request.POST.get('actif') == 'on',
            'obligatoireLettrage': request.POST.get('obligatoireLettrage') == 'on',
            'typeAnalytique': request.POST.get('typeAnalytique', 'AUCUN')
        }
        
        response = api_client.create_compte(compte_data)
        
        if response.success:
            messages.success(request, "Compte créé avec succès")
            return redirect('accounting:plan_comptable')
        else:
            error_msg = response.error.get('message', 'Erreur lors de la création')
            messages.error(request, error_msg)
    
    context = {
        'page_title': 'Nouveau Compte',
        'section': 'accounting'
    }
    
    return render(request, 'accounting/compte_form.html', context)

@login_required
def compte_detail(request, numero):
    """Détail d'un compte"""
    
    # TODO: Implémenter la récupération d'un compte spécifique
    # L'API n'a pas encore cette route dans le client
    
    context = {
        'numero': numero,
        'page_title': f'Compte {numero}',
        'section': 'accounting'
    }
    
    return render(request, 'accounting/compte_detail.html', context)

@login_required
@require_http_methods(["GET"])
def comptes_by_classe_api(request, classe):
    """API endpoint pour récupérer les comptes par classe (AJAX)"""
    
    try:
        response = api_client.get_comptes_by_classe(classe)
        
        if response.success:
            return JsonResponse({
                'success': True,
                'comptes': response.data
            })
        else:
            return JsonResponse({
                'success': False,
                'error': response.error.get('message', 'Erreur')
            })
            
    except Exception as e:
        logger.error(f"Erreur récupération comptes classe {classe}: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': 'Erreur interne du serveur'
        })
