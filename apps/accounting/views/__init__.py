# apps/accounting/views/__init__.py
from .dashboard import dashboard, get_kpis_api, test_api_connection
from .plan_comptable import (
    plan_comptable, compte_list, compte_create, compte_detail, 
    comptes_by_classe_api
)
from .ecritures import (
    ecriture_list, ecriture_create, ecriture_detail, ecriture_edit
)
from .journaux import journal_list, journal_create, journal_detail
from .etats import (
    etats_menu, balance_generale, bilan, compte_resultat, grand_livre
)
from .lettrage import lettrage, lettrage_auto

__all__ = [
    'dashboard', 'get_kpis_api', 'test_api_connection',
    'plan_comptable', 'compte_list', 'compte_create', 'compte_detail', 'comptes_by_classe_api',
    'ecriture_list', 'ecriture_create', 'ecriture_detail', 'ecriture_edit',
    'journal_list', 'journal_create', 'journal_detail',
    'etats_menu', 'balance_generale', 'bilan', 'compte_resultat', 'grand_livre',
    'lettrage', 'lettrage_auto',
]
