# apps/accounting/views/journaux.py
from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
import logging

from ..services.api_client import api_client
from ..services.helpers import handle_api_response

logger = logging.getLogger(__name__)

@login_required
def journal_list(request):
    """Liste des journaux comptables"""
    
    response = api_client.get_journaux()
    journaux = handle_api_response(response, request) or []
    
    context = {
        'journaux': journaux,
        'page_title': 'Journaux Comptables',
        'section': 'accounting'
    }
    
    return render(request, 'accounting/journal_list.html', context)

@login_required
def journal_create(request):
    """Création d'un nouveau journal"""
    
    if request.method == 'POST':
        journal_data = {
            'code': request.POST.get('code'),
            'libelle': request.POST.get('libelle'),
            'type': request.POST.get('type'),
            'prefixeNumero': request.POST.get('prefixeNumero'),
            'prochainNumero': int(request.POST.get('prochainNumero', 1)),
            'actif': request.POST.get('actif') == 'on'
        }
        
        response = api_client.create_journal(journal_data)
        
        if response.success:
            messages.success(request, "Journal créé avec succès")
            return redirect('accounting:journal_list')
        else:
            error_msg = response.error.get('message', 'Erreur lors de la création')
            messages.error(request, error_msg)
    
    context = {
        'page_title': 'Nouveau Journal',
        'section': 'accounting'
    }
    
    return render(request, 'accounting/journal_form.html', context)

@login_required
def journal_detail(request, code):
    """Détail d'un journal"""
    
    # TODO: Implémenter la récupération d'un journal spécifique
    # et des écritures de ce journal
    
    context = {
        'code': code,
        'page_title': f'Journal {code}',
        'section': 'accounting'
    }
    
    return render(request, 'accounting/journal_detail.html', context)
