# apps/accounting/views/dashboard.py
from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.core.cache import cache
from django.views.decorators.http import require_http_methods
from datetime import datetime, date
import logging

from ..services.api_client import api_client
from ..services.helpers import handle_api_response, get_current_exercice_dates

logger = logging.getLogger(__name__)

@login_required
def dashboard(request):
    """Dashboard comptable avec KPIs"""
    
    # Essayer de récupérer depuis le cache
    cache_key = 'dashboard_kpis'
    kpis = cache.get(cache_key)
    
    if not kpis:
        # Récupérer les KPIs depuis l'API
        exercice_dates = get_current_exercice_dates()
        
        response = api_client.get_kpi_financiers(
            exercice_dates['debut'], 
            exercice_dates['fin']
        )
        kpis = handle_api_response(response, request)
        
        if kpis:
            cache.set(cache_key, kpis, timeout=300)  # Cache 5 minutes
    
    # Récupérer les alertes
    alertes_response = api_client.get_alertes()
    alertes = handle_api_response(alertes_response, request) or []
    
    # Récupérer la config société
    config_response = api_client.get_config()
    config = handle_api_response(config_response, request) or {}
    
    context = {
        'kpis': kpis or {},
        'alertes': alertes,
        'societe': config.get('societe', {}),
        'page_title': 'Dashboard Comptable',
        'section': 'accounting'
    }
    
    return render(request, 'accounting/dashboard.html', context)

@login_required
@require_http_methods(["GET"])
def get_kpis_api(request):
    """API endpoint pour récupérer les KPIs (AJAX)"""
    
    try:
        exercice_dates = get_current_exercice_dates()
        
        response = api_client.get_kpi_financiers(
            exercice_dates['debut'], 
            exercice_dates['fin']
        )
        
        if response.success:
            return JsonResponse({
                'success': True,
                'data': response.data
            })
        else:
            return JsonResponse({
                'success': False,
                'error': response.error.get('message', 'Erreur lors de la récupération des KPIs')
            })
            
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des KPIs: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': 'Erreur interne du serveur'
        })

@login_required
@require_http_methods(["GET"])
def test_api_connection(request):
    """Test de connexion à l'API comptabilité"""
    
    try:
        response = api_client.get_config()
        
        if response.success:
            return JsonResponse({
                'success': True,
                'message': 'Connexion API réussie',
                'data': {
                    'societe': response.data.get('societe', {}),
                    'status_code': response.status_code
                }
            })
        else:
            return JsonResponse({
                'success': False,
                'error': response.error.get('message', 'Erreur de connexion API'),
                'status_code': response.status_code
            })
            
    except Exception as e:
        logger.error(f"Erreur test connexion API: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': f'Erreur de connexion: {str(e)}',
            'status_code': 500
        })
