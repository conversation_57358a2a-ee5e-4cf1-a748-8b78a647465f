# apps/accounting/views/lettrage.py
from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
import logging

from ..services.api_client import api_client
from ..services.helpers import handle_api_response

logger = logging.getLogger(__name__)

@login_required
def lettrage(request):
    """Interface de lettrage manuel"""
    
    compte_numero = request.GET.get('compteNumero')
    
    # TODO: Récupérer les lignes non lettrées pour le compte
    # Nécessite une API spécifique
    
    context = {
        'compte_numero': compte_numero,
        'page_title': 'Lettrage',
        'section': 'accounting'
    }
    
    return render(request, 'accounting/lettrage.html', context)

@login_required
@require_http_methods(["POST"])
def lettrage_auto(request):
    """Lettrage automatique"""
    
    compte_numero = request.POST.get('compteNumero')
    tolerance = float(request.POST.get('tolerance', 0.01))
    
    if not compte_numero:
        return JsonResponse({
            'success': False,
            'error': 'Numéro de compte requis'
        })
    
    try:
        response = api_client.lettrage_automatique(compte_numero, tolerance)
        
        if response.success:
            return JsonResponse({
                'success': True,
                'message': 'Lettrage automatique effectué avec succès',
                'data': response.data
            })
        else:
            return JsonResponse({
                'success': False,
                'error': response.error.get('message', 'Erreur lors du lettrage automatique')
            })
            
    except Exception as e:
        logger.error(f"Erreur lettrage automatique: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': 'Erreur interne du serveur'
        })
