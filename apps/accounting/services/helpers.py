# apps/accounting/services/helpers.py
"""
Fonctions utilitaires pour l'intégration Django
"""

from django.contrib import messages
from django.http import JsonResponse
from .api_client import APIResponse

def handle_api_response(response: APIResponse, request, success_message: str = None):
    """
    Gestionnaire standardisé des réponses API pour Django
    """
    if response.success:
        if success_message:
            messages.success(request, success_message)
        return response.data
    else:
        error_message = response.error.get('message', 'Erreur inconnue') if response.error else 'Erreur API'
        messages.error(request, error_message)
        return None

def format_currency(amount: float, currency: str = 'XOF') -> str:
    """Formater un montant en devise"""
    if amount is None:
        return "0 FCFA"
    
    if currency == 'XOF':
        return f"{amount:,.0f} FCFA".replace(',', ' ')
    return f"{amount:,.2f} {currency}"

def validate_syscohada_account(numero: str) -> bool:
    """Valider un numéro de compte SYSCOHADA"""
    if not numero or not numero.isdigit():
        return False
    
    if len(numero) < 3 or len(numero) > 10:
        return False
    
    # Vérifier que la classe est valide (1-8)
    classe = int(numero[0])
    return 1 <= classe <= 8

def get_classe_libelle(classe: int) -> str:
    """Retourner le libellé d'une classe comptable SYSCOHADA"""
    classes = {
        1: "Comptes de ressources durables",
        2: "Comptes d'actif immobilisé",
        3: "Comptes de stocks",
        4: "Comptes de tiers",
        5: "Comptes de trésorerie",
        6: "Comptes de charges",
        7: "Comptes de produits",
        8: "Comptes de résultats"
    }
    return classes.get(classe, f"Classe {classe}")

def validate_equilibrage(lignes: list) -> dict:
    """Valider l'équilibrage d'une écriture comptable"""
    total_debit = sum(float(ligne.get('debit', 0)) for ligne in lignes)
    total_credit = sum(float(ligne.get('credit', 0)) for ligne in lignes)
    
    return {
        'equilibre': abs(total_debit - total_credit) < 0.01,
        'total_debit': total_debit,
        'total_credit': total_credit,
        'difference': total_debit - total_credit
    }

def format_date_fr(date_str: str) -> str:
    """Formater une date ISO en format français"""
    from datetime import datetime
    try:
        date_obj = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
        return date_obj.strftime('%d/%m/%Y')
    except:
        return date_str

def get_current_exercice_dates():
    """Retourner les dates de début et fin de l'exercice en cours"""
    from datetime import datetime, date
    current_year = datetime.now().year
    return {
        'debut': date(current_year, 1, 1).isoformat(),
        'fin': date(current_year, 12, 31).isoformat()
    }
