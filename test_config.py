#!/usr/bin/env python3
"""
Test simple de la configuration du module comptabilité
"""

import os
import sys

# Ajouter le répertoire du projet au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_structure():
    """Test de la structure des fichiers créés"""
    
    print("🔍 Test de la structure du module comptabilité...")
    
    # Vérifier les dossiers principaux
    directories = [
        'apps/accounting/services',
        'apps/accounting/views',
        'apps/accounting/templates/accounting',
        'apps/accounting/static/accounting/css',
        'apps/accounting/static/accounting/js',
        'apps/accounting/templatetags',
        'apps/accounting/management/commands',
        'logs'
    ]
    
    for directory in directories:
        if os.path.exists(directory):
            print(f"✅ {directory}")
        else:
            print(f"❌ {directory}")
    
    # Vérifier les fichiers principaux
    files = [
        'apps/accounting/services/api_client.py',
        'apps/accounting/services/helpers.py',
        'apps/accounting/views/__init__.py',
        'apps/accounting/views/dashboard.py',
        'apps/accounting/urls.py',
        'apps/accounting/templates/accounting/base.html',
        'apps/accounting/templates/accounting/dashboard.html',
        'apps/accounting/static/accounting/css/accounting.css',
        'apps/accounting/static/accounting/js/accounting.js',
        'apps/accounting/templatetags/accounting_tags.py',
        'apps/accounting/management/commands/test_api_connection.py',
        '.env.example'
    ]
    
    for file in files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file}")

def test_imports():
    """Test des imports Python"""
    
    print("\n🔍 Test des imports Python...")
    
    try:
        # Test import du client API
        from apps.accounting.services.api_client import ComptabiliteAPIClient, api_client
        print("✅ Import du client API")
        
        # Test import des helpers
        from apps.accounting.services.helpers import format_currency, validate_syscohada_account
        print("✅ Import des helpers")
        
        # Test des fonctions helpers
        formatted = format_currency(1000000)
        print(f"✅ Format currency: {formatted}")
        
        valid = validate_syscohada_account("411000")
        print(f"✅ Validation compte SYSCOHADA: {valid}")
        
    except ImportError as e:
        print(f"❌ Erreur d'import: {e}")
    except Exception as e:
        print(f"❌ Erreur: {e}")

def test_configuration():
    """Test de la configuration"""
    
    print("\n🔍 Test de la configuration...")
    
    # Variables d'environnement attendues
    env_vars = [
        'COMPTABILITE_API_URL',
        'COMPTABILITE_API_KEY',
        'COMPTABILITE_SOCIETE_ID',
        'COMPTABILITE_SOCIETE_NOM'
    ]
    
    print("Variables d'environnement dans .env.example:")
    if os.path.exists('.env.example'):
        with open('.env.example', 'r') as f:
            content = f.read()
            for var in env_vars:
                if var in content:
                    print(f"✅ {var}")
                else:
                    print(f"❌ {var}")
    else:
        print("❌ .env.example non trouvé")

if __name__ == "__main__":
    print("🚀 Test de configuration du module comptabilité Lotus Web")
    print("=" * 60)
    
    test_structure()
    test_imports()
    test_configuration()
    
    print("\n" + "=" * 60)
    print("✅ Tests terminés !")
    print("\n📋 Prochaines étapes:")
    print("1. Copier .env.example vers .env")
    print("2. Configurer les variables API dans .env")
    print("3. Tester la connexion avec: python manage.py test_api_connection")
    print("4. Lancer le serveur Django et accéder à /comptabilite/")
