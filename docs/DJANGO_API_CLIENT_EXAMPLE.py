# apps/accounting/services/api_client.py
"""
Client API optimisé pour l'architecture mono-société
Utilise les nouvelles routes /api/v1/mono/* qui injectent automatiquement l'ID société
"""

import requests
import logging
from django.conf import settings
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class APIResponse:
    """Classe pour standardiser les réponses API"""
    success: bool
    data: Any = None
    error: Dict = None
    status_code: int = 200

class ComptabiliteAPIClient:
    """
    Client API pour l'architecture mono-société
    Utilise les routes simplifiées qui n'exigent pas de passer societeId
    """
    
    def __init__(self):
        self.base_url = settings.COMPTABILITE_API_URL
        self.api_key = settings.COMPTABILITE_API_KEY
        self.headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
    
    def _make_request(self, method: str, endpoint: str, data: Dict = None, params: Dict = None) -> APIResponse:
        """Méthode générique pour les requêtes API"""
        url = f"{self.base_url}/mono{endpoint}"
        
        try:
            response = requests.request(
                method=method,
                url=url,
                headers=self.headers,
                json=data,
                params=params,
                timeout=30
            )
            
            response_data = response.json()
            
            return APIResponse(
                success=response_data.get('success', False),
                data=response_data.get('data'),
                error=response_data.get('error'),
                status_code=response.status_code
            )
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Erreur API {method} {endpoint}: {str(e)}")
            return APIResponse(
                success=False,
                error={'code': 'NETWORK_ERROR', 'message': str(e)},
                status_code=500
            )
    
    # ==========================================
    # CONFIGURATION SOCIÉTÉ
    # ==========================================
    
    def get_config(self) -> APIResponse:
        """Récupérer la configuration de la société"""
        return self._make_request('GET', '/config')
    
    def get_stats(self) -> APIResponse:
        """Récupérer les statistiques de la société"""
        return self._make_request('GET', '/stats')
    
    # ==========================================
    # PLAN COMPTABLE
    # ==========================================
    
    def get_comptes(self, classe: int = None, actif: bool = None) -> APIResponse:
        """Lister les comptes comptables"""
        params = {}
        if classe:
            params['classe'] = classe
        if actif is not None:
            params['actif'] = actif
        
        return self._make_request('GET', '/comptes', params=params)
    
    def get_hierarchie_comptes(self) -> APIResponse:
        """Récupérer la hiérarchie des comptes"""
        return self._make_request('GET', '/comptes/hierarchie')
    
    def get_comptes_by_classe(self, classe: int) -> APIResponse:
        """Récupérer les comptes d'une classe"""
        return self._make_request('GET', f'/comptes/classe/{classe}')
    
    def create_compte(self, compte_data: Dict) -> APIResponse:
        """Créer un nouveau compte"""
        return self._make_request('POST', '/comptes', data=compte_data)
    
    # ==========================================
    # ÉCRITURES COMPTABLES
    # ==========================================
    
    def get_ecritures(self, filters: Dict = None) -> APIResponse:
        """Lister les écritures comptables"""
        return self._make_request('GET', '/ecritures', params=filters or {})
    
    def create_ecriture(self, ecriture_data: Dict) -> APIResponse:
        """Créer une nouvelle écriture"""
        return self._make_request('POST', '/ecritures', data=ecriture_data)
    
    def get_ecriture(self, ecriture_id: str) -> APIResponse:
        """Récupérer une écriture par ID"""
        return self._make_request('GET', f'/ecritures/{ecriture_id}')
    
    # ==========================================
    # JOURNAUX
    # ==========================================
    
    def get_journaux(self) -> APIResponse:
        """Lister les journaux comptables"""
        return self._make_request('GET', '/journaux')
    
    def create_journal(self, journal_data: Dict) -> APIResponse:
        """Créer un nouveau journal"""
        return self._make_request('POST', '/journaux', data=journal_data)
    
    # ==========================================
    # DASHBOARD
    # ==========================================
    
    def get_kpi_financiers(self, date_debut: str, date_fin: str) -> APIResponse:
        """Récupérer les KPIs financiers"""
        params = {
            'dateDebut': date_debut,
            'dateFin': date_fin
        }
        return self._make_request('GET', '/dashboard/kpi', params=params)
    
    def get_alertes(self) -> APIResponse:
        """Récupérer les alertes financières"""
        return self._make_request('GET', '/dashboard/alertes')
    
    # ==========================================
    # ÉTATS COMPTABLES
    # ==========================================
    
    def get_balance(self, date_debut: str, date_fin: str, niveau: int = 3) -> APIResponse:
        """Récupérer la balance générale"""
        params = {
            'dateDebut': date_debut,
            'dateFin': date_fin,
            'niveau': niveau
        }
        return self._make_request('GET', '/etats/balance', params=params)
    
    def get_bilan(self, date_arrete: str) -> APIResponse:
        """Récupérer le bilan comptable"""
        params = {'dateArrete': date_arrete}
        return self._make_request('GET', '/etats/bilan', params=params)
    
    def get_compte_resultat(self, date_debut: str, date_fin: str) -> APIResponse:
        """Récupérer le compte de résultat"""
        params = {
            'dateDebut': date_debut,
            'dateFin': date_fin
        }
        return self._make_request('GET', '/etats/compte-resultat', params=params)
    
    def get_grand_livre(self, compte_numero: str, date_debut: str, date_fin: str) -> APIResponse:
        """Récupérer le grand livre d'un compte"""
        params = {
            'dateDebut': date_debut,
            'dateFin': date_fin
        }
        return self._make_request('GET', f'/etats/grand-livre/{compte_numero}', params=params)
    
    # ==========================================
    # LETTRAGE
    # ==========================================
    
    def lettrer_ecritures(self, lignes_ids: List[str], lettrage: str) -> APIResponse:
        """Lettrer des écritures"""
        data = {
            'lignesIds': lignes_ids,
            'lettrage': lettrage
        }
        return self._make_request('POST', '/lettrage/lettrer', data=data)
    
    def lettrage_automatique(self, compte_numero: str, tolerance: float = 0.01) -> APIResponse:
        """Effectuer un lettrage automatique"""
        data = {
            'compteNumero': compte_numero,
            'tolerance': tolerance
        }
        return self._make_request('POST', '/lettrage/auto', data=data)
    
    # ==========================================
    # TEMPLATES
    # ==========================================
    
    def get_templates(self) -> APIResponse:
        """Lister les templates d'écritures"""
        return self._make_request('GET', '/templates')
    
    def create_template(self, template_data: Dict) -> APIResponse:
        """Créer un template d'écriture"""
        return self._make_request('POST', '/templates', data=template_data)
    
    # ==========================================
    # IMPORT/EXPORT
    # ==========================================
    
    def import_excel(self, file_path: str, options: Dict = None) -> APIResponse:
        """Importer des écritures depuis Excel"""
        # Note: Cette méthode nécessiterait une implémentation spéciale pour l'upload de fichiers
        # Voir la documentation complète pour les détails
        pass
    
    def export_ecritures(self, filters: Dict = None) -> APIResponse:
        """Exporter les écritures"""
        return self._make_request('GET', '/export/ecritures', params=filters or {})


# Instance globale du client API
api_client = ComptabiliteAPIClient()


# apps/accounting/services/helpers.py
"""
Fonctions utilitaires pour l'intégration Django
"""

def handle_api_response(response: APIResponse, success_message: str = None):
    """
    Gestionnaire standardisé des réponses API pour Django
    """
    from django.contrib import messages
    from django.http import JsonResponse
    
    if response.success:
        if success_message:
            messages.success(request, success_message)
        return response.data
    else:
        error_message = response.error.get('message', 'Erreur inconnue') if response.error else 'Erreur API'
        messages.error(request, error_message)
        return None

def format_currency(amount: float, currency: str = 'XOF') -> str:
    """Formater un montant en devise"""
    if currency == 'XOF':
        return f"{amount:,.0f} FCFA".replace(',', ' ')
    return f"{amount:,.2f} {currency}"

def validate_syscohada_account(numero: str) -> bool:
    """Valider un numéro de compte SYSCOHADA"""
    if not numero or not numero.isdigit():
        return False
    
    if len(numero) < 3 or len(numero) > 10:
        return False
    
    # Vérifier que la classe est valide (1-8)
    classe = int(numero[0])
    return 1 <= classe <= 8
