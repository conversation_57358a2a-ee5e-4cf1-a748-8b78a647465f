# Chronogramme Final - Intégration Comptabilité Optimisée

**Architecture :** Routes mono-société `/api/v1/mono/*` + Guide d'intégration Django  
**Période :** <PERSON><PERSON> → <PERSON>di (Samedi = 4h)

## Avantages de la Nouvelle Architecture

### ✅ **Routes simplifiées**
```bash
# Avant
GET /api/v1/comptes?societeId=uuid-societe

# Maintenant  
GET /api/v1/mono/comptes  # societeId injecté automatiquement
```

### ✅ **Client API optimisé**
- Pas de gestion manuelle du `societeId`
- Gestion standardisée des erreurs
- Cache intégré pour les performances

### ✅ **Sécurité renforcée**
- Clé API configurée avec `societeId` en métadonnées
- Accès uniquement aux données de la société configurée
- Isolation naturelle des données

## Planning Optimisé (5 jours)

| Jour | <PERSON><PERSON><PERSON> | Objectif Principal | Modules |
|------|-------|-------------------|---------|
| **MARDI** | 8h | Configuration + Client API | Service API + Tests connexion |
| **MERCREDI** | 8h | Dashboard + Plan Comptable | KPIs + Hiérarchie comptes |
| **JEUDI** | 8h | Écritures Comptables | Saisie multi-lignes + Liste |
| **VENDREDI** | 8h | États + Journaux | Balance, bilan + Gestion journaux |
| **SAMEDI** | 4h | Lettrage + Finalisation | Module complet intégré |

## Détail Quotidien

### MARDI - Configuration + Client API (8h)

**Matin (4h) - Configuration**
- [ ] **Copier le client API fourni**
  - `apps/accounting/services/api_client.py`
  - `apps/accounting/services/helpers.py`
- [ ] **Configuration settings**
  ```python
  # settings/base.py
  COMPTABILITE_API_URL = 'http://localhost:3000/api/v1'
  COMPTABILITE_API_KEY = 'sk_your_mono_societe_key'
  COMPTABILITE_SOCIETE_ID = 'uuid-de-votre-societe'
  ```
- [ ] **Structure apps/accounting/ selon le guide**
- [ ] **URLs et navigation sidebar**

**Après-midi (4h) - Tests et validation**
- [ ] **Tests connexion API**
  ```python
  # Test de base
  response = api_client.get_config()
  print(response.success, response.data)
  ```
- [ ] **Validation configuration société**
- [ ] **Tests des principales méthodes API**
- [ ] **Gestion des erreurs et timeouts**

---

### MERCREDI - Dashboard + Plan Comptable (8h)

**Matin (4h) - Dashboard**
- [ ] **Vue dashboard selon l'exemple fourni**
  - KPIs financiers (CA, résultat, marge)
  - Alertes financières
  - Actions rapides
- [ ] **Template dashboard responsive**
- [ ] **Graphiques Chart.js pour évolution**
- [ ] **Cache des KPIs (5 minutes)**

**Après-midi (4h) - Plan comptable**
- [ ] **Vue hiérarchique des comptes**
  - Arbre expand/collapse
  - Filtres par classe (1-8)
  - Recherche de comptes
- [ ] **Formulaire création compte**
- [ ] **Validation SYSCOHADA**
- [ ] **Auto-complétion comptes (AJAX)**

---

### JEUDI - Écritures Comptables (8h)

**Matin (4h) - Saisie écritures**
- [ ] **Formulaire multi-lignes selon l'exemple**
  - JavaScript dynamique (ajouter/supprimer lignes)
  - Auto-complétion comptes
  - Validation équilibrage temps réel
- [ ] **Calculs automatiques totaux**
- [ ] **Validation SYSCOHADA côté client**

**Après-midi (4h) - Gestion écritures**
- [ ] **Liste écritures avec filtres**
  - Par date, journal, statut
  - Recherche textuelle
  - Pagination
- [ ] **Détail écriture + actions**
- [ ] **Validation/modification écritures**
- [ ] **Templates d'écritures de base**

---

### VENDREDI - États + Journaux (8h)

**Matin (4h) - États comptables**
- [ ] **Balance générale**
  - Paramètres période/niveau
  - Export Excel/PDF
- [ ] **Bilan comptable SYSCOHADA**
- [ ] **Compte de résultat**
- [ ] **Grand livre par compte**

**Après-midi (4h) - Journaux**
- [ ] **Gestion journaux comptables**
- [ ] **Consultation par journal**
- [ ] **Numérotation automatique**
- [ ] **Import/Export de base**

---

### SAMEDI - Lettrage + Finalisation (4h)

**Matin uniquement (4h)**
- [ ] **Interface lettrage**
  - Sélection lignes à lettrer
  - Lettrage automatique
  - Propositions lettrage
- [ ] **Tests d'intégration complets**
- [ ] **Optimisations UX/UI**
- [ ] **Documentation utilisateur**

## Configuration Technique

### 1. Variables d'environnement
```bash
# .env
COMPTABILITE_API_URL=http://localhost:3000/api/v1
COMPTABILITE_API_KEY=sk_your_mono_societe_key
COMPTABILITE_SOCIETE_ID=uuid-de-votre-societe
COMPTABILITE_SOCIETE_NOM="Ma Société SARL"
```

### 2. Clé API mono-société
```bash
# Créer une clé API avec métadonnées société
curl -X POST http://localhost:3000/api/v1/api-keys \
  -H "Authorization: Bearer ADMIN_API_KEY" \
  -d '{
    "name": "Frontend Django - Ma Société",
    "permissions": ["read", "write"],
    "metadata": {
      "societeId": "uuid-de-ma-societe",
      "type": "mono-societe",
      "frontend": "django"
    }
  }'
```

### 3. Structure finale
```
apps/accounting/
├── services/
│   ├── api_client.py      # Client API fourni
│   ├── helpers.py         # Utilitaires fournis
│   └── cache_service.py   # Cache Redis
├── views/
│   ├── dashboard.py       # Exemple fourni
│   ├── plan_comptable.py
│   ├── ecritures.py
│   └── etats.py
├── forms/
│   ├── ecriture_forms.py  # Exemple fourni
│   └── compte_forms.py
├── templates/accounting/
│   ├── dashboard.html     # Exemple fourni
│   ├── plan_comptable.html
│   └── ecritures/
└── static/accounting/
    ├── css/
    └── js/
```

## Navigation Intégrée

### Sidebar Lotus (section ajoutée)
```html
<div class="accordion-item border-0">
    <h2 class="accordion-header">
        <button class="accordion-button collapsed">
            <i class="bi bi-calculator fs-16 me-2"></i>
            <span>Comptabilité</span>
        </button>
    </h2>
    <div class="accordion-collapse collapse">
        <ul class="list-unstyled">
            <li><a href="/comptabilite/">Dashboard</a></li>
            <li><a href="/comptabilite/plan-comptable/">Plan comptable</a></li>
            <li><a href="/comptabilite/ecritures/">Écritures</a></li>
            <li><a href="/comptabilite/etats/">États comptables</a></li>
            <li><a href="/comptabilite/lettrage/">Lettrage</a></li>
        </ul>
    </div>
</div>
```

## APIs Utilisées (Routes Mono-Société)

### Principales routes simplifiées :
- `GET /mono/config` - Configuration société
- `GET /mono/comptes` - Plan comptable
- `POST /mono/ecritures` - Créer écriture
- `GET /mono/dashboard/kpi` - KPIs financiers
- `GET /mono/etats/balance` - Balance générale
- `GET /mono/etats/bilan` - Bilan comptable
- `POST /mono/lettrage/auto` - Lettrage automatique

## Livrables Finaux

### Module comptabilité intégré dans Lotus :
- ✅ **Dashboard** KPIs avec cache optimisé
- ✅ **Plan comptable** hiérarchique avec recherche
- ✅ **Saisie écritures** multi-lignes avec validation
- ✅ **États comptables** (balance, bilan, grand livre)
- ✅ **Journaux** avec numérotation automatique
- ✅ **Lettrage** manuel et automatique
- ✅ **Navigation** intégrée dans sidebar Lotus
- ✅ **Style** cohérent avec l'existant
- ✅ **Performance** optimisée avec cache Redis

### Avantages de cette approche :
- **Développement 40% plus rapide** (routes simplifiées)
- **Code plus propre** (pas de gestion manuelle societeId)
- **Sécurité renforcée** (isolation automatique)
- **Maintenance facilitée** (client API standardisé)

## Points d'Attention

### Configuration initiale critique :
1. **Créer la société** dans l'API externe
2. **Configurer la clé API** avec métadonnées société
3. **Tester la connexion** avec `GET /mono/config`
4. **Valider les permissions** de la clé

### Priorités développement :
- **Jour 1** : Configuration API (critique)
- **Jour 2** : Dashboard + Plan comptable (base)
- **Jour 3** : Saisie écritures (fonctionnalité clé)
- **Jour 4** : États comptables (important)
- **Jour 5** : Lettrage + finitions

---

**Objectif :** Comptabilité complète avec architecture optimisée ! 🚀
