# Guide de Travail - 2 Développeurs Comptabilité

**Objectif :** Intégrer le module comptabilité SYSCOHADA dans Lotus Web avec 2 développeurs en parallèle  
**Durée :** 3-4 jours au lieu de 5 jours solo  
**Architecture :** Routes mono-société `/api/v1/mono/*` + Client API fourni

## Répartition des Rôles

### 👤 **DEV A - Lead Frontend/UX**
**Spécialité :** Interface utilisateur, dashboard, états comptables  
**Responsabilités :** Templates, CSS/JS, graphiques, exports

### 👤 **DEV B - Lead Backend/Logic**  
**Spécialité :** Logique métier, formulaires, validation  
**Responsabilités :** Vues Django, formulaires, validation SYSCOHADA

---

## Planning Détaillé (4 jours)

### **JOUR 1 - Configuration Commune (8h)**
**🤝 Travail en binôme - Configuration critique**

#### Matin (4h) - Setup projet
- [ ] **DEV A + DEV B ensemble**
  - Copier le client API fourni (`api_client.py`, `helpers.py`)
  - Configuration settings Django
  - Structure `apps/accounting/` selon le guide
  - Tests connexion API de base

#### Après-midi (4h) - Base commune
- [ ] **DEV A** : Navigation sidebar + templates de base
- [ ] **DEV B** : URLs routing + vues de base
- [ ] **Validation commune** : Tests API + structure

**🎯 Livrable Jour 1 :** Configuration API + Structure de base

---

### **JOUR 2 - Développement Parallèle (8h)**

#### **👤 DEV A - Dashboard + Interface**

**Matin (4h)**
- [ ] **Dashboard comptable**
  ```python
  # apps/accounting/views/dashboard.py
  def dashboard(request):
      api_client = ComptabiliteAPIClient()
      kpis = api_client.get_kpi_financiers(date_debut, date_fin)
      # Template avec KPIs + graphiques
  ```
- [ ] **Template dashboard responsive**
- [ ] **Graphiques Chart.js** (CA, résultat, évolution)

**Après-midi (4h)**
- [ ] **Templates de base accounting**
  - `base.html` avec navigation
  - Composants réutilisables (cards, modals)
  - CSS spécifique module comptabilité

#### **👤 DEV B - Plan Comptable + Logique**

**Matin (4h)**
- [ ] **Vues plan comptable**
  ```python
  # apps/accounting/views/comptes.py
  def plan_comptable(request):
      api_client = ComptabiliteAPIClient()
      hierarchie = api_client.get_hierarchie_comptes()
      # Logique hiérarchie + filtres
  ```
- [ ] **Formulaires comptes**
- [ ] **Validation SYSCOHADA**

**Après-midi (4h)**
- [ ] **Auto-complétion comptes** (AJAX)
- [ ] **Filtres par classe** (1-8)
- [ ] **Recherche de comptes**

**🎯 Livrable Jour 2 :** Dashboard + Plan comptable fonctionnels

---

### **JOUR 3 - Spécialisation (8h)**

#### **👤 DEV A - États Comptables**

**Matin (4h)**
- [ ] **Balance générale**
  ```python
  def balance_generale(request):
      # Paramètres période/niveau
      # Template avec tableau responsive
      # Export Excel/PDF
  ```
- [ ] **Interface paramètres** (dates, niveaux)

**Après-midi (4h)**
- [ ] **Bilan comptable SYSCOHADA**
- [ ] **Compte de résultat**
- [ ] **Export Excel/PDF** des rapports

#### **👤 DEV B - Écritures Comptables**

**Matin (4h)**
- [ ] **Formulaire saisie multi-lignes**
  ```python
  # apps/accounting/forms.py
  class EcritureForm(forms.Form):
      # Formset pour lignes multiples
      # Validation équilibrage débit/crédit
  ```
- [ ] **JavaScript dynamique** (ajouter/supprimer lignes)

**Après-midi (4h)**
- [ ] **Liste écritures** avec filtres
- [ ] **Validation temps réel** équilibrage
- [ ] **Templates d'écritures** de base

**🎯 Livrable Jour 3 :** États comptables + Saisie écritures

---

### **JOUR 4 - Finalisation (8h)**

#### **👤 DEV A - UX/UI + Export**

**Matin (4h)**
- [ ] **Interface lettrage**
- [ ] **Optimisations UX/UI**
- [ ] **Responsive design** (mobile/tablet)

**Après-midi (4h)**
- [ ] **Grand livre** par compte
- [ ] **Journaux comptables**
- [ ] **Polish interface** finale

#### **👤 DEV B - Tests + Validation**

**Matin (4h)**
- [ ] **Tests d'intégration** complets
- [ ] **Validation SYSCOHADA** approfondie
- [ ] **Gestion erreurs** API

**Après-midi (4h)**
- [ ] **Lettrage automatique**
- [ ] **Corrections bugs**
- [ ] **Documentation** utilisateur

**🎯 Livrable Jour 4 :** Module comptabilité complet et testé

---

## Organisation du Travail

### **🔄 Synchronisation Quotidienne**

#### **Daily Standup (15min - 9h00)**
- Avancement de la veille
- Objectifs du jour
- Blocages éventuels

#### **Point Technique (30min - 14h00)**
- Validation intégration
- Résolution conflits Git
- Décisions techniques

#### **Review Fin de Journée (15min - 17h45)**
- Démo des fonctionnalités
- Planification lendemain
- Merge des branches

### **📁 Organisation Git**

#### **Branches de travail**
```bash
main
├── feature/dashboard-kpis          # DEV A
├── feature/plan-comptable          # DEV B
├── feature/etats-comptables        # DEV A
├── feature/ecritures-saisie        # DEV B
└── feature/lettrage-finalisation   # Commun
```

#### **Workflow Git**
1. **Créer branche** par fonctionnalité
2. **Commits fréquents** avec messages clairs
3. **Pull Request** avec review de l'autre dev
4. **Merge** après validation

### **💬 Communication**

#### **Outils recommandés**
- **Slack/Discord** : Communication rapide
- **GitHub Issues** : Suivi des tâches
- **Shared Screen** : Résolution problèmes complexes

#### **Conventions de code**
- **PEP 8** pour Python
- **Noms explicites** pour variables/fonctions
- **Commentaires** pour logique complexe
- **Docstrings** pour fonctions publiques

---

## Répartition Détaillée des Tâches

### **👤 DEV A - Frontend/UX (Responsabilités)**

#### **Templates & Interface**
- [ ] Dashboard avec KPIs et graphiques
- [ ] Templates responsive cohérents
- [ ] Navigation intégrée dans sidebar
- [ ] CSS/JS spécifique comptabilité

#### **États & Rapports**
- [ ] Balance générale avec paramètres
- [ ] Bilan comptable SYSCOHADA
- [ ] Compte de résultat
- [ ] Export Excel/PDF
- [ ] Grand livre par compte

#### **UX/UI**
- [ ] Interface lettrage
- [ ] Optimisations responsive
- [ ] Polish final interface
- [ ] Tests utilisateur

### **👤 DEV B - Backend/Logic (Responsabilités)**

#### **Logique Métier**
- [ ] Vues Django pour toutes les fonctionnalités
- [ ] Formulaires avec validation
- [ ] Gestion des erreurs API
- [ ] Cache et optimisations

#### **Plan Comptable**
- [ ] Hiérarchie des comptes
- [ ] CRUD comptes avec validation SYSCOHADA
- [ ] Auto-complétion et recherche
- [ ] Filtres par classe

#### **Écritures Comptables**
- [ ] Formulaire multi-lignes dynamique
- [ ] Validation équilibrage temps réel
- [ ] Liste avec filtres avancés
- [ ] Templates d'écritures

#### **Tests & Validation**
- [ ] Tests d'intégration complets
- [ ] Validation SYSCOHADA approfondie
- [ ] Lettrage automatique
- [ ] Documentation technique

---

## Points de Synchronisation Critiques

### **🚨 Dépendances Inter-Dev**

#### **DEV B → DEV A**
- Vues Django créées → Templates peuvent être développés
- Formulaires validés → Interface peut être finalisée
- APIs testées → Graphiques peuvent utiliser vraies données

#### **DEV A → DEV B**
- Templates de base → Vues peuvent référencer les bons templates
- CSS/JS → Formulaires peuvent utiliser les styles
- Interface validée → Logique peut être ajustée

### **🔄 Points de Validation Commune**

#### **Fin Jour 1**
- [ ] Configuration API validée par les 2 devs
- [ ] Structure projet approuvée
- [ ] Tests connexion API passants

#### **Fin Jour 2**
- [ ] Dashboard fonctionnel (DEV A)
- [ ] Plan comptable opérationnel (DEV B)
- [ ] Intégration testée ensemble

#### **Fin Jour 3**
- [ ] États comptables (DEV A)
- [ ] Saisie écritures (DEV B)
- [ ] Tests d'intégration croisés

#### **Fin Jour 4**
- [ ] Module complet testé
- [ ] Interface cohérente validée
- [ ] Documentation complète

---

## Livrables Finaux

### **Module comptabilité intégré :**
- ✅ **Dashboard** KPIs avec graphiques (DEV A)
- ✅ **Plan comptable** hiérarchique (DEV B)
- ✅ **Saisie écritures** multi-lignes (DEV B)
- ✅ **États comptables** complets (DEV A)
- ✅ **Interface lettrage** (DEV A)
- ✅ **Tests d'intégration** (DEV B)
- ✅ **Documentation** utilisateur (DEV B)

### **Avantages 2 devs :**
- **Temps divisé par 2** : 4 jours au lieu de 5+ jours
- **Qualité améliorée** : Code review croisé
- **Spécialisation** : Chacun sur son domaine d'expertise
- **Moins de blocages** : Entraide et résolution rapide

---

**🎯 Objectif : Module comptabilité complet en 4 jours avec 2 développeurs !**
